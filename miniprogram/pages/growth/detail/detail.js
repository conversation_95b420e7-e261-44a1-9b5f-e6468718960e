// 成长V4 - 打卡详情页面重构版
// 导入API模块
const checkinAPI = require("../../../apis/checkin.js");
const contentAPI = require("../../../apis/content.js");
const { STORAGE_KEYS } = require("../../../utils/constants.js");
const { childrenActions } = require("../../../utils/index.js");

Page({
  data: {
    // 加载状态
    loading: true,
    campLoading: true,
    checkinLoading: true,

    // API数据
    campId: null,
    childId: null,
    apiCalendarData: [], // 存储API返回的日历数据

    // 错误状态
    error: null,

    // 训练营信息
    campInfo: {
      id: 1,
      title: "21天跳绳挑战",
      subtitle: "从零基础到连续跳绳300个",
      startTime: "2024年1月15日",
      currentDay: 14,
      totalDays: 21,
      progressPercent: 67,
      streakDays: 12,
      totalPoints: 1680,
    },

    // 今日状态
    todayStatus: "pending", // pending, completed

    // 是否有契约
    hasContract: true,

    // 契约信息
    contractInfo: {
      status: "进行中",
      reward: "新的跳绳 + 全家游乐园",
      witnesses: [
        { id: 1, avatar: "/images/avatar_default.png" },
        { id: 2, avatar: "/images/avatar_default.png" },
      ],
    },

    // 相关视频列表
    videosList: [
      {
        id: 1,
        title: "跳绳==基础动作教学===",
        subtitle: "正确握绳姿势与站立方法",
        duration: "5:30",
        watched: true,
        isPlaying: false,
      },
      {
        id: 2,
        title: "节奏训练技巧",
        subtitle: "找到适合的跳绳节奏感",
        duration: "8:15",
        watched: true,
        isPlaying: false,
      },
      {
        id: 3,
        title: "花样跳绳进阶",
        subtitle: "从几个到几十个的突破",
        duration: "6:45",
        watched: false,
        isPlaying: true, // 当前正在播放
      },
      {
        id: 4,
        title: "常见错误纠正",
        subtitle: "常见错误纠正与进阶",
        duration: "4:20",
        watched: false,
        isPlaying: false,
      },
      {
        id: 5,
        title: "体能提升训练",
        subtitle: "综合体能提升方法",
        duration: "7:10",
        watched: false,
        isPlaying: false,
      },
      {
        id: 6,
        title: "双人跳绳配合",
        subtitle: "21天成果检验",
        duration: "9:30",
        watched: false,
        isPlaying: false,
      },
    ],

    // 滚动指示器状态
    canScrollLeft: false,
    canScrollRight: true,

    // 日历导航状态
    currentWeekIndex: 0, // 当前周的索引
    currentMonthIndex: 0, // 当前月的索引
    totalWeeks: 3, // 总共的周数（21天 = 3周）
    totalMonths: 2, // 总共的月数（1月和2月）

    // 训练营详情展开状态
    campInfoExpanded: false,

    // 日历相关
    calendarExpanded: false,

    // 标准日历数据
    calendarWeeks: [], // 标准日历周数据
    weekdays: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],

    // 补卡信息
    makeupInfo: {
      totalCount: 3,
      usedCount: 0,
      availableCount: 3,
    },

    // 每日奖励展开状态
    rewardsExpanded: false,

    // 当前周数据
    currentWeek: [
      {
        dayName: "周一",
        date: "15",
        fullDate: "1月15日",
        status: "completed",
        canMakeup: false,
      },
      {
        dayName: "周二",
        date: "16",
        fullDate: "1月16日",
        status: "completed",
        canMakeup: false,
      },
      {
        dayName: "周三",
        date: "17",
        fullDate: "1月17日",
        status: "missed",
        canMakeup: true,
      },
      {
        dayName: "周四",
        date: "18",
        fullDate: "1月18日",
        status: "completed",
        canMakeup: false,
      },
      {
        dayName: "周五",
        date: "19",
        fullDate: "1月19日",
        status: "today",
        canMakeup: false,
      },
      {
        dayName: "周六",
        date: "20",
        fullDate: "1月20日",
        status: "future",
        canMakeup: false,
      },
      {
        dayName: "周日",
        date: "21",
        fullDate: "1月21日",
        status: "future",
        canMakeup: false,
      },
    ],

    // 当前月数据
    currentMonthDays: [
      // 这里应该是完整的月份数据，为了演示简化
      { day: 15, fullDate: "1月15日", status: "completed", canMakeup: false },
      { day: 16, fullDate: "1月16日", status: "completed", canMakeup: false },
      { day: 17, fullDate: "1月17日", status: "missed", canMakeup: true },
      { day: 18, fullDate: "1月18日", status: "completed", canMakeup: false },
      { day: 19, fullDate: "1月19日", status: "today", canMakeup: false },
      { day: 20, fullDate: "1月20日", status: "future", canMakeup: false },
      { day: 21, fullDate: "1月21日", status: "future", canMakeup: false },
    ],

    // 补卡相关
    makeupChances: 3,
    showMakeupModal: false,
    selectedDay: null,

    // 每日奖励
    availableRewards: 2,
    dailyRewards: [
      { day: 1, icon: "🏅", name: "新手勋章", status: "claimed" },
      { day: 3, icon: "⭐", name: "坚持之星", status: "claimed" },
      { day: 7, icon: "🔥", name: "一周达成", status: "available" },
      { day: 14, icon: "💎", name: "半月英雄", status: "available" },
      { day: 21, icon: "👑", name: "完美挑战", status: "locked" },
    ],
  },

  onLoad(options) {
    console.log("打卡详情页面加载", options);

    // 获取URL参数
    const campId = options.camp_id ? parseInt(options.camp_id) : null;

    // 获取当前选择的孩子ID
    const currentChild = childrenActions.getCurrentChild();
    const childId = currentChild ? currentChild.id : null;

    if (!campId) {
      this.setData({
        error: "训练营ID缺失",
        loading: false,
      });
      wx.showToast({
        title: "参数错误",
        icon: "none",
      });
      return;
    }

    if (!childId) {
      this.setData({
        error: "请先选择孩子",
        loading: false,
      });
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      return;
    }

    // 设置基础数据
    this.setData({
      campId: campId,
      childId: childId,
    });

    // 加载所有数据
    this.loadAllData();

    // 初始化月视图数据（保留原有逻辑作为fallback）
    this.generateMonthData();

    // 初始化周视图数据并清除选中状态
    this.initWeekData();

    // 检查视频列表滚动状态
    this.checkScrollStatus();
  },

  /**
   * 初始化周视图数据
   */
  initWeekData() {
    // 不再使用硬编码的测试数据，等待API数据加载完成后自动更新
    // API数据会通过 processCampCheckinCalendar -> generateCalendarFromApiData 来设置
    console.log("initWeekData: 等待API数据加载...");

    // 设置初始的空数据，避免页面报错
    this.setData({
      currentWeek: [],
      currentMonthDays: [],
    });
  },

  /**
   * 生成完整的月份数据
   */
  generateMonthData() {
    const monthDays = [];
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth(); // 0-11
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // 生成当月所有日期
    for (let day = 1; day <= daysInMonth; day++) {
      let status = "future";
      let canMakeup = false;

      // 模拟一些打卡状态，确保与周视图数据一致
      if (day < 15) {
        status = Math.random() > 0.3 ? "completed" : "missed";
        canMakeup = status === "missed" && Math.random() > 0.5;
      } else if (day === 15) {
        status = "completed";
      } else if (day === 16) {
        status = "completed";
      } else if (day === 17) {
        status = "missed";
        canMakeup = true;
      } else if (day === 18) {
        status = "completed";
      } else if (day === 19) {
        status = "today";
      } else if (day < 19) {
        status = Math.random() > 0.2 ? "completed" : "missed";
        canMakeup = status === "missed";
      }

      monthDays.push({
        day: day,
        fullDate: `1月${day}日`,
        status: status,
        canMakeup: canMakeup,
        selected: false, // 初始化选中状态为false
      });
    }

    this.setData({
      currentMonthDays: monthDays,
    });
  },

  /**
   * 切换训练营信息展开状态
   */
  toggleCampInfo() {
    this.setData({
      campInfoExpanded: !this.data.campInfoExpanded,
    });
  },

  /**
   * 切换每日奖励展开状态
   */
  toggleRewards() {
    this.setData({
      rewardsExpanded: !this.data.rewardsExpanded,
    });
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      this.setData({ loading: true, error: null });

      console.log("🚀 开始加载所有数据:", {
        campId: this.data.campId,
        childId: this.data.childId,
      });

      // 并行加载数据，但允许部分失败
      const results = await Promise.allSettled([
        this.loadCampDetail(),
        this.loadUserCampStatus(),
        this.loadCheckinData(),
      ]);

      // 检查结果
      const failures = results.filter((result) => result.status === "rejected");
      if (failures.length > 0) {
        console.warn(
          "部分数据加载失败:",
          failures.map((f) => f.reason)
        );
      }

      this.setData({
        loading: false,
        error: null,
      });

      console.log("✅ 数据加载完成");
    } catch (error) {
      console.error("❌ 加载数据失败:", error);
      this.setData({
        loading: false,
        error: error.message || "数据加载失败",
      });

      wx.showToast({
        title: "数据加载失败",
        icon: "none",
      });
    }
  },

  /**
   * 加载训练营详情
   */
  async loadCampDetail() {
    try {
      this.setData({ campLoading: true });

      const campDetail = await contentAPI.getCampDetail(this.data.campId);
      console.log("训练营详情:", campDetail);

      // 转换数据格式
      const transformedCampInfo = this.transformCampInfo(campDetail);

      this.setData({
        campInfo: transformedCampInfo,
        campLoading: false,
      });
    } catch (error) {
      console.error("加载训练营详情失败:", error);
      this.setData({ campLoading: false });
      throw error;
    }
  },

  /**
   * 加载用户训练营参与状态
   */
  async loadUserCampStatus() {
    try {
      const userCamps = await contentAPI.getUserCamps();
      console.log("用户训练营列表:", userCamps);

      // 找到当前训练营的参与状态
      const currentCamp = userCamps.find(
        (camp) => camp.camp_id === this.data.campId
      );

      if (currentCamp) {
        // 更新训练营进度信息
        this.updateCampProgress(currentCamp);
      } else {
        console.log("用户未参与该训练营");
        // 可以显示参与按钮或提示
      }
    } catch (error) {
      console.error("加载用户训练营状态失败:", error);
      throw error;
    }
  },

  /**
   * 加载打卡相关数据
   */
  async loadCheckinData() {
    try {
      this.setData({ checkinLoading: true });

      // 调试：检查参数值
      console.log("🔍 loadCheckinData 参数检查:", {
        childId: this.data.childId,
        campId: this.data.campId,
        childIdType: typeof this.data.childId,
        campIdType: typeof this.data.campId,
      });

      // 验证必需参数
      if (!this.data.childId) {
        throw new Error("childId 参数缺失");
      }
      if (!this.data.campId) {
        throw new Error("campId 参数缺失");
      }

      // 并行加载打卡相关数据
      const [todayStatus, checkinCalendar, checkinStats] = await Promise.all([
        this.loadTodayStatus(),
        this.loadCampCheckinCalendar(),
        this.loadCheckinStats(),
      ]);

      // 处理打卡日历数据
      this.processCampCheckinCalendar(checkinCalendar);

      // 更新统计信息
      this.updateCheckinStats(checkinStats);

      this.setData({ checkinLoading: false });
    } catch (error) {
      console.error("加载打卡数据失败:", error);
      this.setData({ checkinLoading: false });
      throw error;
    }
  },

  /**
   * 加载今日打卡状态
   */
  async loadTodayStatus() {
    try {
      console.log("🔍 loadTodayStatus 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const todayStatus = await checkinAPI.getTodayCheckinStatus(
        this.data.childId,
        this.data.campId
      );
      console.log("✅ 今日打卡状态:", todayStatus);

      this.setData({
        todayStatus: todayStatus.has_checked_in ? "completed" : "pending",
      });

      return todayStatus;
    } catch (error) {
      console.error("❌ 获取今日打卡状态失败:", error);
      console.error("错误详情:", error.message, error.code);
      return null;
    }
  },

  /**
   * 加载打卡历史
   */
  async loadCheckinHistory() {
    try {
      console.log("🔍 loadCheckinHistory 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const history = await checkinAPI.getCheckinHistory({
        child_id: this.data.childId,
        camp_id: this.data.campId,
        page: 1,
        limit: 100,
      });
      console.log("✅ 打卡历史:", history);
      return history;
    } catch (error) {
      console.error("❌ 获取打卡历史失败:", error);
      console.error("错误详情:", error.message, error.code);
      return { list: [] };
    }
  },

  /**
   * 加载打卡统计
   */
  async loadCheckinStats() {
    try {
      console.log("🔍 loadCheckinStats 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const stats = await checkinAPI.getCheckinStats(
        this.data.childId,
        this.data.campId
      );
      console.log("✅ 打卡统计:", stats);
      return stats;
    } catch (error) {
      console.error("❌ 获取打卡统计失败:", error);
      console.error("错误详情:", error.message, error.code);
      return {};
    }
  },

  /**
   * 加载训练营打卡日历
   */
  async loadCampCheckinCalendar() {
    try {
      console.log("📅 loadCampCheckinCalendar 调用参数:", {
        childId: this.data.childId,
        campId: this.data.campId,
      });

      const calendar = await checkinAPI.getCampCheckinCalendar(
        this.data.campId,
        this.data.childId
      );
      console.log("✅ 训练营打卡日历:", calendar);
      return calendar;
    } catch (error) {
      console.error("❌ 获取训练营打卡日历失败:", error);
      console.error("错误详情:", error.message, error.code);
      return {
        calendar_data: { dates: [] },
        makeup_info: { total_count: 0, used_count: 0, available_count: 0 },
      };
    }
  },

  /**
   * 加载训练营数据（保留原方法名兼容性）
   */
  loadCampData(campId) {
    console.log("加载训练营数据", campId);
    // 重定向到新的加载方法
    this.loadAllData();
  },

  /**
   * 转换训练营信息
   */
  transformCampInfo(campDetail) {
    const now = new Date();
    const startDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000); // 假设开始了14天

    return {
      id: campDetail.id,
      title: campDetail.title || "训练营",
      subtitle: campDetail.subtitle || "",
      startTime: this.formatDate(startDate),
      currentDay: 14, // 这个值应该从用户参与状态中获取
      totalDays: campDetail.duration_days || 21,
      progressPercent: Math.round(
        (14 / (campDetail.duration_days || 21)) * 100
      ),
      streakDays: 0, // 从统计数据中获取
      totalPoints: 0, // 从统计数据中获取
    };
  },

  /**
   * 更新训练营进度信息
   */
  updateCampProgress(userCamp) {
    const campInfo = { ...this.data.campInfo };

    campInfo.currentDay = userCamp.current_day || 0;
    campInfo.progressPercent = userCamp.progress_percentage || 0;
    campInfo.startTime = this.formatDate(new Date(userCamp.participation_date));

    this.setData({ campInfo });
  },

  /**
   * 处理打卡历史数据
   */
  processCheckinHistory(historyData) {
    if (!historyData || !historyData.list) {
      return;
    }

    const checkinMap = new Map();
    historyData.list.forEach((record) => {
      const date = new Date(record.checkin_date);
      const dateKey = this.formatDateKey(date);
      checkinMap.set(dateKey, {
        status: record.status === 2 ? "makeup" : "completed",
        points: record.points_earned || 0,
        date: date,
      });
    });

    // 生成日历数据
    this.generateCalendarFromHistory(checkinMap);
  },

  /**
   * 处理训练营打卡日历数据
   */
  processCampCheckinCalendar(calendarData) {
    if (!calendarData) {
      console.warn("日历数据为空，使用默认数据");
      return;
    }

    // 新API响应格式：{ data: { camp_info, calendar_data, makeup_info } }
    const responseData = calendarData;
    console.log(responseData);
    if (
      !responseData ||
      !responseData.calendar_data ||
      !responseData.calendar_data.dates
    ) {
      console.warn("日历数据格式不正确，使用默认数据");
      return;
    }

    const { camp_info, calendar_data, makeup_info } = responseData;
    const dates = calendar_data.dates;

    // 更新训练营信息（如果API提供了更准确的信息）
    if (camp_info) {
      const updatedCampInfo = {
        ...this.data.campInfo,
        id: camp_info.id || this.data.campInfo.id,
        title: camp_info.title || this.data.campInfo.title,
        subtitle: camp_info.subtitle || this.data.campInfo.subtitle,
        totalDays: camp_info.total_days || this.data.campInfo.totalDays,
        currentDay: calendar_data.current_day || this.data.campInfo.currentDay,
        startTime: calendar_data.start_date
          ? this.formatDate(new Date(calendar_data.start_date))
          : this.data.campInfo.startTime,
      };

      this.setData({ campInfo: updatedCampInfo });
    }

    // 更新补卡信息
    this.setData({
      makeupInfo: {
        totalCount: makeup_info.total_count || 0,
        usedCount: makeup_info.used_count || 0,
        availableCount: makeup_info.available_count || 0,
      },
    });

    // 生成日历数据
    this.generateCalendarFromApiData(dates);
  },

  /**
   * 从API数据生成日历数据
   */
  generateCalendarFromApiData(dates) {
    // 保存API数据
    this.setData({
      apiCalendarData: dates,
    });

    // 生成标准日历数据
    this.generateStandardCalendar(dates);
  },

  /**
   * 生成标准日历数据
   */
  generateStandardCalendar(dates) {
    const today = new Date();

    // 将API数据转换为Map便于查找
    const dateMap = new Map();
    dates.forEach((dateInfo) => {
      const date = new Date(dateInfo.date);
      const dateKey = this.formatDateKey(date);
      dateMap.set(dateKey, {
        status: this.mapApiStatusToLocal(dateInfo.status),
        canMakeup: dateInfo.can_makeup || false,
        dayNumber: dateInfo.day_number,
        dateType: dateInfo.date_type,
      });
    });

    // 生成日历周数据
    const calendarWeeks = this.data.calendarExpanded
      ? this.generateAllWeeks(dateMap)
      : this.generateDefaultWeeks(dateMap);

    this.setData({
      calendarWeeks,
    });
  },

  /**
   * 生成默认显示的2周数据
   */
  generateDefaultWeeks(dateMap) {
    const today = new Date();
    const currentWeekStart = this.getWeekStart(today);
    const weeks = [];

    // 生成2周数据：上周 + 本周
    for (let weekOffset = -1; weekOffset <= 0; weekOffset++) {
      const weekStart = new Date(
        currentWeekStart.getTime() + weekOffset * 7 * 24 * 60 * 60 * 1000
      );
      const week = this.generateWeekData(weekStart, weekOffset, dateMap);
      weeks.push(week);
    }

    return weeks;
  },

  /**
   * 生成全部训练营周数据（展开时使用）
   */
  generateAllWeeks(dateMap) {
    if (!this.data.apiCalendarData || this.data.apiCalendarData.length === 0) {
      return this.generateDefaultWeeks(dateMap);
    }

    // 找到训练营的开始和结束日期
    const dates = this.data.apiCalendarData.map((item) => new Date(item.date));
    const startDate = new Date(Math.min(...dates));
    const endDate = new Date(Math.max(...dates));

    const startWeek = this.getWeekStart(startDate);
    const endWeek = this.getWeekStart(endDate);

    const weeks = [];
    let currentWeek = new Date(startWeek);
    let weekIndex = 0;

    while (currentWeek <= endWeek) {
      const week = this.generateWeekData(currentWeek, weekIndex, dateMap);
      weeks.push(week);

      currentWeek = new Date(currentWeek.getTime() + 7 * 24 * 60 * 60 * 1000);
      weekIndex++;
    }

    return weeks;
  },

  /**
   * 生成单周数据
   */
  generateWeekData(weekStart, weekIndex, dateMap) {
    const weekDays = [];
    const today = new Date();

    for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
      const date = new Date(
        weekStart.getTime() + dayOffset * 24 * 60 * 60 * 1000
      );
      const dayData = this.generateDayData(date, dateMap, today);
      weekDays.push(dayData);
    }

    return {
      weekIndex,
      weekDays,
    };
  },

  /**
   * 生成单日数据
   */
  generateDayData(date, dateMap, today) {
    const dateKey = this.formatDateKey(date);
    const dateInfo = dateMap.get(dateKey);
    const isToday = this.isSameDay(date, today);
    const isClickable = date <= today;

    // 确定显示文本（跨月处理）
    const displayText = this.formatDateDisplay(date);

    // 确定是否为当前月
    const isCurrentMonth =
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    // 确定状态和图标
    let status = "blank";
    let statusIcon = "";
    let isCheckinDay = false;

    if (dateInfo && dateInfo.dateType === "checkin") {
      isCheckinDay = true;
      status = dateInfo.status;
      statusIcon = this.getStatusIcon(status);
    } else if (date > today) {
      status = "future";
    }

    return {
      date: dateKey,
      day: date.getDate(),
      month: date.getMonth() + 1,
      displayText,
      status,
      statusIcon,
      isToday,
      isClickable,
      isCheckinDay,
      isCurrentMonth,
      canMakeup: dateInfo ? dateInfo.canMakeup : false,
      weekDay: date.getDay() === 0 ? 7 : date.getDay(), // 周一=1, 周日=7
    };
  },

  /**
   * 获取周的开始日期（周一）
   */
  getWeekStart(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    return new Date(d.setDate(diff));
  },

  /**
   * 格式化日期显示文本（跨月处理）
   */
  formatDateDisplay(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}/${day}`;
  },

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    const iconMap = {
      pending: "⭕",
      completed: "✅",
      makeup: "🔄",
      missed: "❌",
      blank: "",
      future: "",
    };
    return iconMap[status] || "";
  },

  /**
   * 判断两个日期是否为同一天
   */
  isSameDay(date1, date2) {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  },

  /**
   * 日历日期点击事件
   */
  onDayTap(e) {
    const day = e.currentTarget.dataset.day;

    if (!day.isClickable) {
      wx.showToast({
        title: "未来日期不可操作",
        icon: "none",
      });
      return;
    }

    if (!day.isCheckinDay) {
      wx.showToast({
        title: "非打卡日",
        icon: "none",
      });
      return;
    }

    // 处理打卡逻辑
    this.handleCheckinAction(day);
  },

  /**
   * 处理打卡操作
   */
  handleCheckinAction(day) {
    console.log("处理打卡操作:", day);

    if (day.status === "pending") {
      // 今日打卡
      this.performCheckin(day);
    } else if (day.status === "missed" && day.canMakeup) {
      // 补打卡
      this.performMakeupCheckin(day);
    } else {
      // 查看打卡详情
      this.viewCheckinDetail(day);
    }
  },

  /**
   * 执行打卡
   */
  async performCheckin(day) {
    try {
      wx.showLoading({ title: "打卡中..." });

      // 调用打卡API
      await checkinAPI.createCheckin(this.data.childId, this.data.campId);

      wx.hideLoading();
      wx.showToast({
        title: "打卡成功！",
        icon: "success",
      });

      // 重新加载日历数据
      this.loadCampCheckinCalendar().then((calendar) => {
        this.processCampCheckinCalendar(calendar);
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: "打卡失败",
        icon: "none",
      });
      console.error("打卡失败:", error);
    }
  },

  /**
   * 基于API数据生成指定周的数据
   */
  generateWeekDataFromApi(weekStartDate) {
    if (!this.data.apiCalendarData || !this.data.apiCalendarData.length) {
      console.warn("API数据未加载，无法生成周数据");
      return;
    }

    const currentWeek = [];

    // 将API数据转换为Map便于查找
    const dateMap = new Map();
    this.data.apiCalendarData.forEach((dateInfo) => {
      const date = new Date(dateInfo.date);
      const dateKey = this.formatDateKey(date);
      dateMap.set(dateKey, {
        status: this.mapApiStatusToLocal(dateInfo.status),
        canMakeup: dateInfo.can_makeup || false,
        dayNumber: dateInfo.day_number,
        dateType: dateInfo.date_type,
      });
    });

    // 生成指定周的7天数据
    for (let i = 0; i < 7; i++) {
      const date = new Date(weekStartDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = this.formatDateKey(date);
      const dateInfo = dateMap.get(dateKey);

      currentWeek.push({
        dayName: this.getDayName(date.getDay()),
        date: date.getDate().toString(),
        fullDate: this.formatFullDate(date),
        status: dateInfo ? dateInfo.status : this.getDefaultDateStatus(date),
        canMakeup: dateInfo ? dateInfo.canMakeup : false,
        selected: false,
      });
    }

    this.setData({
      currentWeek,
    });
  },

  /**
   * 将API状态映射到本地状态
   */
  mapApiStatusToLocal(apiStatus) {
    // API返回的是字符串状态，直接映射
    const statusMap = {
      pending: "pending", // 待打卡
      completed: "completed", // 已完成
      makeup: "makeup", // 补打卡
      missed: "missed", // 已错过
      skipped: "skipped", // 已跳过
    };
    return statusMap[apiStatus] || "pending";
  },

  /**
   * 获取默认日期状态（用于API数据中不存在的日期）
   */
  getDefaultDateStatus(date) {
    const today = new Date();
    const isToday = this.isSameDate(date, today);
    const isPast = date < today;

    if (isToday) {
      return "today";
    } else if (isPast) {
      return "missed";
    } else {
      return "future";
    }
  },

  /**
   * 从打卡历史生成日历数据（保留兼容性）
   */
  generateCalendarFromHistory(checkinMap) {
    const today = new Date();
    const currentWeek = [];
    const currentMonthDays = [];

    // 生成当前周数据
    const startOfWeek = this.getStartOfWeek(today);
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = this.formatDateKey(date);
      const checkinRecord = checkinMap.get(dateKey);

      currentWeek.push({
        dayName: this.getDayName(date.getDay()),
        date: date.getDate().toString(),
        fullDate: this.formatFullDate(date),
        status: this.getDateStatus(date, checkinRecord),
        canMakeup: this.canMakeupDate(date, checkinRecord),
        selected: false,
      });
    }

    // 生成当前月数据
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateKey = this.formatDateKey(date);
      const checkinRecord = checkinMap.get(dateKey);

      currentMonthDays.push({
        day: day,
        fullDate: this.formatFullDate(date),
        status: this.getDateStatus(date, checkinRecord),
        canMakeup: this.canMakeupDate(date, checkinRecord),
        selected: false,
      });
    }

    this.setData({
      currentWeek,
      currentMonthDays,
    });
  },

  /**
   * 更新打卡统计信息
   */
  updateCheckinStats(stats) {
    if (!stats) return;

    const campInfo = { ...this.data.campInfo };
    campInfo.streakDays = stats.consecutive_days || 0;
    campInfo.totalPoints = stats.total_points || 0;

    this.setData({ campInfo });
  },

  /**
   * 获取日期状态
   */
  getDateStatus(date, checkinRecord) {
    const today = new Date();
    const isToday = this.isSameDate(date, today);
    const isPast = date < today;
    const isFuture = date > today;

    if (isToday) {
      return checkinRecord ? "completed" : "today";
    } else if (isPast) {
      return checkinRecord ? "completed" : "missed";
    } else {
      return "future";
    }
  },

  /**
   * 判断是否可以补打卡
   */
  canMakeupDate(date, checkinRecord) {
    const today = new Date();
    const daysDiff = Math.floor((today - date) / (24 * 60 * 60 * 1000));

    // 只有过去的未打卡日期且在7天内可以补卡
    return (
      !checkinRecord &&
      date < today &&
      daysDiff <= 7 &&
      this.data.makeupChances > 0
    );
  },

  /**
   * 工具方法：格式化日期
   */
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  /**
   * 工具方法：格式化完整日期
   */
  formatFullDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  /**
   * 工具方法：格式化日期键
   */
  formatDateKey(date) {
    return date.toISOString().split("T")[0];
  },

  /**
   * 工具方法：获取周的开始日期（周一）
   */
  getStartOfWeek(date) {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(date.setDate(diff));
  },

  /**
   * 工具方法：获取星期名称
   */
  getDayName(dayIndex) {
    const dayNames = ["日", "一", "二", "三", "四", "五", "六"];
    return dayNames[dayIndex];
  },

  /**
   * 工具方法：判断是否为同一天
   */
  isSameDate(date1, date2) {
    return date1.toDateString() === date2.toDateString();
  },

  /**
   * 获取周标题
   */
  getWeekTitle(weekIndex) {
    const weekTitles = [
      "1月15日 - 1月21日", // 第1周
      "1月22日 - 1月28日", // 第2周
      "1月29日 - 2月4日", // 第3周
    ];
    return weekTitles[weekIndex] || "1月15日 - 1月21日";
  },

  /**
   * 获取周数据
   */
  getWeekData(weekIndex) {
    const weekData = [
      // 第1周数据
      [
        {
          dayName: "一",
          date: "15",
          fullDate: "1月15日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "二",
          date: "16",
          fullDate: "1月16日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "三",
          date: "17",
          fullDate: "1月17日",
          status: "missed",
          canMakeup: true,
          selected: false,
        },
        {
          dayName: "四",
          date: "18",
          fullDate: "1月18日",
          status: "completed",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "五",
          date: "19",
          fullDate: "1月19日",
          status: "today",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "六",
          date: "20",
          fullDate: "1月20日",
          status: "future",
          canMakeup: false,
          selected: false,
        },
        {
          dayName: "日",
          date: "21",
          fullDate: "1月21日",
          status: "future",
          canMakeup: false,
          selected: false,
        },
      ],
      // 第2周数据
      [
        {
          dayName: "一",
          date: "22",
          fullDate: "1月22日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "二",
          date: "23",
          fullDate: "1月23日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "三",
          date: "24",
          fullDate: "1月24日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "四",
          date: "25",
          fullDate: "1月25日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "五",
          date: "26",
          fullDate: "1月26日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "六",
          date: "27",
          fullDate: "1月27日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "日",
          date: "28",
          fullDate: "1月28日",
          status: "future",
          canMakeup: false,
        },
      ],
      // 第3周数据
      [
        {
          dayName: "一",
          date: "29",
          fullDate: "1月29日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "二",
          date: "30",
          fullDate: "1月30日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "三",
          date: "31",
          fullDate: "1月31日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "四",
          date: "1",
          fullDate: "2月1日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "五",
          date: "2",
          fullDate: "2月2日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "六",
          date: "3",
          fullDate: "2月3日",
          status: "future",
          canMakeup: false,
        },
        {
          dayName: "日",
          date: "4",
          fullDate: "2月4日",
          status: "future",
          canMakeup: false,
        },
      ],
    ];
    return weekData[weekIndex] || weekData[0];
  },

  /**
   * 获取月标题
   */
  getMonthTitle(monthIndex) {
    const monthTitles = ["2024年1月", "2024年2月"];
    return monthTitles[monthIndex] || "2024年1月";
  },

  /**
   * 获取月数据
   */
  getMonthData(monthIndex) {
    const monthData = [
      // 1月数据
      [
        { day: 15, fullDate: "1月15日", status: "completed", canMakeup: false },
        { day: 16, fullDate: "1月16日", status: "completed", canMakeup: false },
        { day: 17, fullDate: "1月17日", status: "missed", canMakeup: true },
        { day: 18, fullDate: "1月18日", status: "completed", canMakeup: false },
        { day: 19, fullDate: "1月19日", status: "today", canMakeup: false },
        { day: 20, fullDate: "1月20日", status: "future", canMakeup: false },
        { day: 21, fullDate: "1月21日", status: "future", canMakeup: false },
        { day: 22, fullDate: "1月22日", status: "future", canMakeup: false },
        { day: 23, fullDate: "1月23日", status: "future", canMakeup: false },
        { day: 24, fullDate: "1月24日", status: "future", canMakeup: false },
        { day: 25, fullDate: "1月25日", status: "future", canMakeup: false },
        { day: 26, fullDate: "1月26日", status: "future", canMakeup: false },
        { day: 27, fullDate: "1月27日", status: "future", canMakeup: false },
        { day: 28, fullDate: "1月28日", status: "future", canMakeup: false },
        { day: 29, fullDate: "1月29日", status: "future", canMakeup: false },
        { day: 30, fullDate: "1月30日", status: "future", canMakeup: false },
        { day: 31, fullDate: "1月31日", status: "future", canMakeup: false },
      ],
      // 2月数据
      [
        { day: 1, fullDate: "2月1日", status: "future", canMakeup: false },
        { day: 2, fullDate: "2月2日", status: "future", canMakeup: false },
        { day: 3, fullDate: "2月3日", status: "future", canMakeup: false },
        { day: 4, fullDate: "2月4日", status: "future", canMakeup: false },
        { day: 5, fullDate: "2月5日", status: "future", canMakeup: false },
        { day: 6, fullDate: "2月6日", status: "future", canMakeup: false },
        { day: 7, fullDate: "2月7日", status: "future", canMakeup: false },
      ],
    ];
    return monthData[monthIndex] || monthData[0];
  },

  /**
   * 选择视频事件
   */
  onVideoSelect(e) {
    const videoId = parseInt(e.currentTarget.dataset.videoId);

    // 更新视频列表中的播放状态
    const videosList = this.data.videosList.map((video) => ({
      ...video,
      isPlaying: video.id === videoId,
    }));

    this.setData({
      videosList: videosList,
    });

    // 可以在这里添加其他逻辑，比如自动播放等
    console.log("选中视频:", videoId);

    // 显示播放提示
    const selectedVideo = this.data.videosList.find((v) => v.id === videoId);
    wx.showToast({
      title: `播放视频：${selectedVideo.title}`,
      icon: "none",
    });
  },

  /**
   * 视频列表滚动事件
   */
  onVideoListScroll(e) {
    const { scrollLeft, scrollWidth } = e.detail;
    const query = wx.createSelectorQuery().in(this);
    query.select(".video-text-list").boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const containerWidth = res[0].width;

        this.setData({
          canScrollLeft: scrollLeft > 10, // 滚动超过10px才显示左侧指示器
          canScrollRight: scrollLeft < scrollWidth - containerWidth - 10, // 距离右边界超过10px才显示右侧指示器
        });
      }
    });
  },

  /**
   * 检查滚动状态
   */
  checkScrollStatus() {
    setTimeout(() => {
      const query = wx.createSelectorQuery().in(this);
      query.select(".video-text-list").boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          const containerWidth = res[0].width;
          const videoCount = this.data.videosList.length;
          const itemWidth = 300; // 每个视频项的宽度(包含margin)
          const totalWidth = videoCount * itemWidth;

          this.setData({
            canScrollLeft: false, // 初始状态不显示左侧指示器
            canScrollRight: totalWidth > containerWidth, // 只有内容超出容器宽度才显示右侧指示器
          });
        }
      });
    }, 100); // 延迟100ms确保DOM渲染完成
  },

  /**
   * 切换日历展开状态
   */
  toggleCalendar() {
    const expanded = !this.data.calendarExpanded;

    this.setData({
      calendarExpanded: expanded,
    });

    // 重新生成日历数据
    if (this.data.apiCalendarData && this.data.apiCalendarData.length > 0) {
      this.generateStandardCalendar(this.data.apiCalendarData);
    }
  },

  /**
   * 上一周
   */
  prevWeek() {
    const today = new Date();
    const currentWeekStart = this.getStartOfWeek(today);
    const prevWeekStart = new Date(
      currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000
    );

    // 基于API数据生成上一周的数据
    this.generateWeekDataFromApi(prevWeekStart);

    wx.showToast({
      title: "切换到上一周",
      icon: "none",
      duration: 1000,
    });
  },

  /**
   * 下一周
   */
  nextWeek() {
    const today = new Date();
    const currentWeekStart = this.getStartOfWeek(today);
    const nextWeekStart = new Date(
      currentWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000
    );

    // 基于API数据生成下一周的数据
    this.generateWeekDataFromApi(nextWeekStart);

    wx.showToast({
      title: "切换到下一周",
      icon: "none",
      duration: 1000,
    });
  },

  /**
   * 上一月
   */
  prevMonth() {
    if (this.data.currentMonthIndex > 0) {
      const newIndex = this.data.currentMonthIndex - 1;
      this.setData({
        currentMonthIndex: newIndex,
        currentMonthTitle: this.getMonthTitle(newIndex),
        currentMonthDays: this.getMonthData(newIndex),
      });
    } else {
      wx.showToast({
        title: "已经是第一个月了",
        icon: "none",
      });
    }
  },

  /**
   * 下一月
   */
  nextMonth() {
    if (this.data.currentMonthIndex < this.data.totalMonths - 1) {
      const newIndex = this.data.currentMonthIndex + 1;
      this.setData({
        currentMonthIndex: newIndex,
        currentMonthTitle: this.getMonthTitle(newIndex),
        currentMonthDays: this.getMonthData(newIndex),
      });
    } else {
      wx.showToast({
        title: "已经是最后一个月了",
        icon: "none",
      });
    }
  },

  /**
   * 选择日期
   */
  selectDay(e) {
    const day = e.currentTarget.dataset.day;
    console.log("选择日期:", day);

    // 清除之前的选中状态
    this.clearSelectedState();

    // 找到对应的日期项并设置选中状态
    const updatedCurrentWeek = this.data.currentWeek.map((item) => ({
      ...item,
      selected: item.date === day.date && item.fullDate === day.fullDate,
    }));

    const updatedCurrentMonthDays = this.data.currentMonthDays.map((item) => ({
      ...item,
      selected: item.day === day.day && item.fullDate === day.fullDate,
    }));

    // 更新数据
    this.setData({
      currentWeek: updatedCurrentWeek,
      currentMonthDays: updatedCurrentMonthDays,
    });

    console.log("日期选中状态已更新");

    // 根据日期状态执行相应操作
    if (
      (day.status === "missed" || day.status === "skipped") &&
      day.canMakeup &&
      this.data.makeupInfo.availableCount > 0
    ) {
      // 显示补卡确认弹窗
      this.setData({
        selectedDay: day,
        showMakeupModal: true,
      });
    } else if (day.status === "today") {
      // 今日打卡
      this.goToCheckin();
    } else if (day.status === "completed") {
      // 查看打卡详情
      wx.showToast({
        title: `查看${day.fullDate}打卡记录`,
        icon: "none",
      });
    } else {
      // 其他状态，显示提示信息
      this.showDateStatusTip(day);
    }
  },

  /**
   * 清除所有选中状态
   */
  clearSelectedState() {
    // 清除周视图选中状态
    this.data.currentWeek.forEach((item) => {
      item.selected = false;
    });

    // 清除月视图选中状态
    this.data.currentMonthDays.forEach((item) => {
      item.selected = false;
    });
  },

  /**
   * 显示日期状态提示
   */
  showDateStatusTip(day) {
    let title = "";
    switch (day.status) {
      case "pending":
        title = `${day.fullDate} 待打卡`;
        break;
      case "completed":
        title = `${day.fullDate} 已完成打卡`;
        break;
      case "makeup":
        title = `${day.fullDate} 已补卡`;
        break;
      case "missed":
        if (day.canMakeup) {
          title = `${day.fullDate} 已错过，可补卡`;
        } else {
          title = `${day.fullDate} 已错过`;
        }
        break;
      case "skipped":
        title = `${day.fullDate} 已跳过`;
        break;
      case "future":
        title = `${day.fullDate} 未到时间`;
        break;
      case "today":
        title = `今日打卡`;
        break;
      default:
        title = `${day.fullDate}`;
    }

    wx.showToast({
      title: title,
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 隐藏补卡弹窗
   */
  hideMakeupModal() {
    this.setData({
      showMakeupModal: false,
      selectedDay: null,
    });
  },

  /**
   * 确认补卡 - 跳转到打卡表单页面
   */
  confirmMakeup() {
    const selectedDay = this.data.selectedDay;
    const makeupChances = this.data.makeupChances;

    if (makeupChances <= 0) {
      wx.showToast({
        title: "补卡次数不足",
        icon: "none",
      });
      return;
    }

    if (!selectedDay) {
      wx.showToast({
        title: "请选择补卡日期",
        icon: "none",
      });
      return;
    }

    // 构造补卡日期
    const makeupDate = this.parseDateFromSelectedDay(selectedDay);
    const dateString = makeupDate.toISOString().split("T")[0];

    // 跳转到打卡表单页面，传递补卡参数
    const url = `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=makeup&date=${dateString}&from=detail`;

    console.log("跳转到补卡页面:", url);

    wx.navigateTo({
      url: url,
      success: () => {
        // 关闭补卡弹窗
        this.setData({
          showMakeupModal: false,
          selectedDay: null,
        });
      },
      fail: (error) => {
        console.error("跳转失败:", error);
        wx.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 解析选中日期
   */
  parseDateFromSelectedDay(selectedDay) {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();

    // 从fullDate解析日期，格式如"1月17日"
    const dayMatch = selectedDay.fullDate.match(/(\d+)月(\d+)日/);
    if (dayMatch) {
      const monthNum = parseInt(dayMatch[1]) - 1; // 月份从0开始
      const dayNum = parseInt(dayMatch[2]);
      return new Date(year, monthNum, dayNum);
    }

    // 如果解析失败，使用date字段
    return new Date(year, month, parseInt(selectedDay.date));
  },

  /**
   * 更新补卡后的本地数据
   */
  updateLocalDataAfterMakeup(selectedDay) {
    const currentWeek = [...this.data.currentWeek];
    const currentMonthDays = [...this.data.currentMonthDays];

    // 更新周视图
    const weekIndex = currentWeek.findIndex(
      (item) => item.date === selectedDay.date
    );
    if (weekIndex !== -1) {
      currentWeek[weekIndex].status = "completed";
      currentWeek[weekIndex].canMakeup = false;
    }

    // 更新月视图
    const monthIndex = currentMonthDays.findIndex(
      (item) => item.day === parseInt(selectedDay.date)
    );
    if (monthIndex !== -1) {
      currentMonthDays[monthIndex].status = "completed";
      currentMonthDays[monthIndex].canMakeup = false;
    }

    this.setData({
      currentWeek,
      currentMonthDays,
      makeupChances: this.data.makeupChances - 1,
    });
  },

  /**
   * 刷新统计数据
   */
  async refreshStatsData() {
    try {
      const stats = await checkinAPI.getCheckinStats(this.data.childId);
      this.updateCheckinStats(stats);
    } catch (error) {
      console.error("刷新统计数据失败:", error);
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    try {
      wx.showLoading({ title: "刷新中..." });

      // 重新加载打卡相关数据
      await this.loadCheckinData();

      wx.hideLoading();
      wx.showToast({
        title: "刷新成功",
        icon: "success",
      });
    } catch (error) {
      wx.hideLoading();
      console.error("刷新数据失败:", error);
      wx.showToast({
        title: "刷新失败",
        icon: "none",
      });
    }
  },

  /**
   * 领取奖励
   */
  claimReward(e) {
    const reward = e.currentTarget.dataset.reward;

    if (reward.status === "available") {
      wx.showToast({
        title: `领取${reward.name}成功！`,
        icon: "success",
      });

      // 更新奖励状态
      const dailyRewards = this.data.dailyRewards;
      const index = dailyRewards.findIndex((item) => item.day === reward.day);
      if (index !== -1) {
        dailyRewards[index].status = "claimed";
        this.setData({
          dailyRewards,
          availableRewards: this.data.availableRewards - 1,
        });
      }
    } else if (reward.status === "locked") {
      wx.showToast({
        title: "奖励未解锁",
        icon: "none",
      });
    }
  },

  /**
   * 跳转到打卡页面
   */
  goToCheckin() {
    // 构造今日打卡的URL参数
    const today = new Date().toISOString().split("T")[0];
    const url = `/pages/growth/checkin/checkin?camp_id=${this.data.campId}&type=today&date=${today}&from=detail`;

    console.log("跳转到今日打卡页面:", url);

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error("跳转失败:", error);
        wx.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    // 如果页面已经加载过数据，检查是否需要刷新
    if (this.data.campId && this.data.childId && !this.data.loading) {
      // 只刷新今日状态，避免频繁请求
      this.loadTodayStatus().catch((error) => {
        console.error("刷新今日状态失败:", error);
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshPageData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 重试加载数据
   */
  retryLoadData() {
    if (this.data.campId && this.data.childId) {
      this.loadAllData();
    } else {
      wx.showToast({
        title: "参数缺失，请重新进入",
        icon: "none",
      });
    }
  },

  /**
   * 处理网络错误
   */
  handleNetworkError(error) {
    console.error("网络错误:", error);

    let errorMessage = "网络连接失败";

    if (error.code === -1) {
      errorMessage = "网络连接超时";
    } else if (error.code === 401) {
      errorMessage = "登录已过期，请重新登录";
      // 可以跳转到登录页面
    } else if (error.code === 403) {
      errorMessage = "权限不足";
    } else if (error.code === 404) {
      errorMessage = "请求的资源不存在";
    } else if (error.code >= 500) {
      errorMessage = "服务器繁忙，请稍后重试";
    }

    this.setData({
      error: errorMessage,
    });

    return errorMessage;
  },

  /**
   * 显示错误提示
   */
  showErrorToast(message) {
    wx.showToast({
      title: message || "操作失败",
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 显示加载提示
   */
  showLoading(title = "加载中...") {
    wx.showLoading({
      title: title,
      mask: true,
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 数据缓存管理
   */
  getCacheKey(type) {
    return `camp_detail_${type}_${this.data.campId}_${this.data.childId}`;
  },

  /**
   * 设置缓存数据
   */
  setCacheData(type, data, expireTime = 5 * 60 * 1000) {
    const cacheKey = this.getCacheKey(type);
    const cacheData = {
      data: data,
      timestamp: Date.now(),
      expireTime: expireTime,
    };

    try {
      wx.setStorageSync(cacheKey, cacheData);
    } catch (error) {
      console.error("设置缓存失败:", error);
    }
  },

  /**
   * 获取缓存数据
   */
  getCacheData(type) {
    const cacheKey = this.getCacheKey(type);

    try {
      const cacheData = wx.getStorageSync(cacheKey);
      if (cacheData && cacheData.timestamp) {
        const now = Date.now();
        if (now - cacheData.timestamp < cacheData.expireTime) {
          return cacheData.data;
        } else {
          // 缓存过期，删除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error("获取缓存失败:", error);
    }

    return null;
  },

  /**
   * 清除缓存
   */
  clearCache() {
    const types = ["camp_detail", "checkin_history", "checkin_stats"];
    types.forEach((type) => {
      const cacheKey = this.getCacheKey(type);
      try {
        wx.removeStorageSync(cacheKey);
      } catch (error) {
        console.error("清除缓存失败:", error);
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: `我的${this.data.campInfo.title}进度`,
      path: `/pages/growth/detail/detail?camp_id=${this.data.campInfo.id}`,
      imageUrl: "/images/share_camp_detail.jpg",
    };
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    // 清理定时器等资源
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
  },

  /**
   * 刷新页面数据 - 供打卡页面调用
   */
  async refreshPageData() {
    console.log("🔄 刷新详情页面数据");

    try {
      // 重新加载所有数据
      await this.loadAllData();

      console.log("✅ 详情页面数据刷新成功");
    } catch (error) {
      console.error("❌ 详情页面数据刷新失败:", error);
    }
  },
});
